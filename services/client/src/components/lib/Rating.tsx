import React from 'react';

interface RatingProps {
  rating: number; // The rating value (e.g. 4.86)
  max?: number; // Maximum rating (default is 5)
  className?: string;
}

interface StarProps {
  fillPercentage: number; // A value between 0 and 1 representing how much the star is filled
}

const Star: React.FC<StarProps> = ({ fillPercentage }) => {
  return (
    <div className="relative inline-block w-4 h-4">
      {/* The background star (empty) */}
      <svg
        viewBox="0 0 20 20"
        className="absolute inset-0 w-full h-full text-gray-300"
        fill="currentColor"
      >
        <path d="M9.049 2.927a1 1 0 011.902 0l1.286 3.967a1 1 0 00.95.69h4.183c.969 0 1.371 1.24.588 1.81l-3.388 2.46a1 1 0 00-.364 1.118l1.286 3.967c.3.921-.755 1.688-1.54 1.118l-3.388-2.46a1 1 0 00-1.175 0l-3.388 2.46c-.784.57-1.838-.197-1.54-1.118l1.286-3.967a1 1 0 00-.364-1.118L2.046 9.394c-.783-.57-.38-1.81.588-1.81h4.183a1 1 0 00.95-.69l1.286-3.967z" />
      </svg>
      {/* The filled star with a clip representing the fill percentage */}
      <div
        className="absolute inset-0 overflow-hidden"
        style={{ width: `${fillPercentage * 100}%` }}
      >
        <svg
          viewBox="0 0 20 20"
          className="w-full h-full text-yellow-400"
          fill="currentColor"
        >
          <path d="M9.049 2.927a1 1 0 011.902 0l1.286 3.967a1 1 0 00.95.69h4.183c.969 0 1.371 1.24.588 1.81l-3.388 2.46a1 1 0 00-.364 1.118l1.286 3.967c.3.921-.755 1.688-1.54 1.118l-3.388-2.46a1 1 0 00-1.175 0l-3.388 2.46c-.784.57-1.838-.197-1.54-1.118l1.286-3.967a1 1 0 00-.364-1.118L2.046 9.394c-.783-.57-.38-1.81.588-1.81h4.183a1 1 0 00.95-.69l1.286-3.967z" />
        </svg>
      </div>
    </div>
  );
};

const Rating: React.FC<RatingProps> = ({ rating, max = 5, className = '' }) => {
   const safeRating = typeof rating === 'number' && !isNaN(rating) ? rating : 0;
  const clampedRating = Math.max(0, Math.min(safeRating, max));

  return (
    <div className={`flex items-center ${className}`}>
      {Array.from({ length: max }).map((_, i) => {
        // For each star, calculate how much of it should be filled.
        // For example, if rating is 4.86 then:
        // • Star 1: fill = min(max(4.86 - 0, 0), 1) = 1 (fully filled)
        // • Star 5: fill = min(max(4.86 - 4, 0), 1) = 0.86 (partially filled)
        const fillPercentage = Math.min(Math.max(rating - i, 0), 1);
        return <Star key={i} fillPercentage={fillPercentage} />;
      })}
      <span className="ml-2 text-sm text-gray-700">{clampedRating.toFixed(2)}</span>
    </div>
  );
};

export default Rating;
